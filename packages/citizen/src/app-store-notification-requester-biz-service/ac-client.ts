import {useApiEndpoint} from './api-config';

// Hook to get fetch config with the current endpoint (for use inside React components)
export const useAcClient = () => {
  const masterRemoteConfig = useApiEndpoint();
  console.log(masterRemoteConfig.host, 'useAcClient masterRemoteConfig');

  const baseURL = masterRemoteConfig.host.appStoreNotificationServiceBackendURL;
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...masterRemoteConfig.host.appStoreNotificationServiceBackendHeader,
  };

  // Helper function to make requests with common logic
  const makeRequest = async (
    url: string,
    method: string,
    data?: any,
    options?: RequestInit,
  ) => {
    const response = await fetch(`${baseURL}${url}`, {
      method,
      headers: {
        ...defaultHeaders,
        ...options?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  };

  const fetchClient = {
    get: async (url: string, options?: RequestInit) => {
      return makeRequest(url, 'GET', undefined, options);
    },

    post: async (url: string, data?: any, options?: RequestInit) => {
      return makeRequest(url, 'POST', data, options);
    },

    put: async (url: string, data?: any, options?: RequestInit) => {
      return makeRequest(url, 'PUT', data, options);
    },

    delete: async (url: string, options?: RequestInit) => {
      return makeRequest(url, 'DELETE', undefined, options);
    },

    patch: async (url: string, data?: any, options?: RequestInit) => {
      return makeRequest(url, 'PATCH', data, options);
    },

    // Raw fetch method for custom requests
    fetch: async (url: string, options?: RequestInit) => {
      return fetch(`${baseURL}${url}`, {
        headers: {
          ...defaultHeaders,
          ...options?.headers,
        },
        ...options,
      });
    },
  };

  return fetchClient;
};
