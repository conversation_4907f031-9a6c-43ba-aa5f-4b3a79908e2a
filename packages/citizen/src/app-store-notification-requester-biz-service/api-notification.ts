import {Platform} from 'react-native';
import {useAcClient} from './ac-client';
import {useApiEndpoint} from './api-config';

export const usePostDevice = () => {
  const client = useAcClient();
  const masterRemoteConfig = useApiEndpoint();
  return async (params: any) => {
    try {
      console.log(
        params,
        'PostDevice params',
        masterRemoteConfig.host.appStoreNotificationServiceBackendPath,
      );
      return client.post(
        masterRemoteConfig.host.appStoreNotificationServiceBackendPath,
        {...params, appId: masterRemoteConfig.host.appId},
      );
    } catch (error) {
      console.error('Error in usePostDevice:', error);
      throw error; // Re-throw the error for further handling if needed
    }
  };
};

export const usePatchDevice = () => {
  const client = useAcClient();
  const masterRemoteConfig = useApiEndpoint();
  return async (deviceId: string, params: any) => {
    try {
      return client.patch(
        `${masterRemoteConfig.host.appStoreNotificationServiceBackendPath}/${deviceId}`,
        {
          ...params,
          appId: masterRemoteConfig.host.appId,
        },
      );
    } catch (error) {
      console.error('Error in usePatchDevice:', error);
      throw error; // Re-throw the error for further handling if needed
    }
  };
};
