import React, {useRef, useState} from 'react';
import {
  Text,
  View,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
} from 'react-native';
import {IsPlatformIOS} from '../../../DashboardFn';
import {textStyles} from '../../../styles';
import ExpandableText from '../../../components/BoxCollapsible/ExpandableText';
import {
  ActivityIndicator,
  Button,
  Icon,
  MD3Theme,
  useTheme,
} from 'react-native-paper';
import {Assets} from '../../../assets';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {BoxDocument} from '../../../components/BoxCollapsible';
import {AttachedFileButton} from '../../../components/ButtonComponent';
import {LineSvg} from '../../../icons';
import {useNoiDungGiayTo} from '../../../stores';
import {useAuthStore} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';
import {DocumentPickerResponse} from 'react-native-document-picker';
import Toast from 'react-native-toast-message';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {useDanhSachNoiDungGiayTo} from '../../../useQueryState/UseDanhSachNoiDungGiayTo';
import {useDocumentDetail} from '../../../useQueryState/UseDocumentDetail';
import moment from 'moment';
import {useQueryClient} from 'react-query';
import {AcTextInput} from '../../../components/AcTextInput/AcTextInput';
import {showESign} from '../../../configs/common';

export enum ETypeActionDocument {
  Approve = 'Approve',
  Reject = 'Reject',
}

type ApproveRejectDocumentRouteProp = RouteProp<
  MainStackParamList,
  'ApproveRejectDocument'
>;

export type boxDocumentType = {
  listNguoiKy?: [
    {
      chuKy: string;
      chungThu: string;
      ngayKy: string;
      nguoiKy: string;
      phatHanh: string;
    },
  ];
  totalRecords: number;
  tapTinDinhKemID: number;
  donViID: string;
  quaTrinhXuLyID: string;
  nguoiXuLyHoSoID: string;
  originalName: string;
  uploadName: string;
  pathName: string;
  fileSize: string;
  userID: string;
  sealed: string;
  sealedDate: string;
  signerID: string;
  hoSoID: string;
  createdUserID: string;
  createdDate: string;
  lastUpdUserID: string;
  lastUpdDate: string;
  totalDVC: number;
};

interface FormValues {
  rejectReason: string;
  delayReason: string;
}
const prepareRejectPayload = (
  chiTietHoSo: any,
  user: any,
  NguyenDoTre: string,
) => {
  const payload = {
    thongTinKhongPheDuyet: {
      khongPheDuyetID: 0,
      hoSoID: chiTietHoSo?.thongTinThuLyHoSo?.hoSoID,
      lyDoID: null,
      ngayXuLy: moment().format('YYYY-MM-DD'),
      noiDungXuLy: '',
      yKienXuLy: '',
      lyDoKhac: '',
      nguoiKyID: null,
      nguoiXuLyID: user?.user_PortalID,
      phongBanID: user?.phongBanID,
      donViID: user?.donViID,
      QuaTrinhXuLyID: chiTietHoSo.quaTrinhXulyHoSo?.find(
        (_: {quaTrinhHienTai: boolean}) => _.quaTrinhHienTai === true,
      )?.quaTrinhXuLyID,
    },
    nguoiXuLyHoSo: {
      nguoiXuLyHoSoHienTaiID:
        chiTietHoSo?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID,
      noiDungXuLy: '',
      nguoiXuLyID: user?.user_PortalID,
      phongBanXuLyID: user?.phongBanID,
      donViXuLyID: user?.donViID,
      yKienCuaLanhDao: false,
      lyDoTreHanID: null,
      nguyenDoTre: NguyenDoTre,
    },
  };
  return payload;
};

export const ApproveRejectDocument = () => {
  const route = useRoute<ApproveRejectDocumentRouteProp>();
  const {actionType, ...params} = route.params ?? {};

  const {user} = useAuthStore();
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const styles = React.useMemo(() => themedStyles(theme), [theme]);

  const scrollViewRef = useRef<ScrollView>(null);
  const queryClient = useQueryClient();
  const {data} = useDocumentDetail({
    ChucNangHienTai: params.ChucNangHienTai,
    ChucNangKeTiep: params.ChucNangKeTiep,
    DonViID: user?.donViID,
    HoSoID: Number.parseInt(params.DocId, 10),
    LoaiXuLy: params.LoaiXuLy.toString(),
    UserID: user?.user_PortalID,
  });

  const {dsHoSoLocal} = useNoiDungGiayTo();
  const {
    data: dataNoiDungGiayTo,
    isLoading: isLoadingNoiDungGiayTo,
    isRefetching: isRefetchingNoiDungGiayTo,
  } = useDanhSachNoiDungGiayTo({
    DonViID: user?.donViID,
    HoSoID: params?.hoSoID,
    UserID: user?.user_PortalID,
    PageNum: 1,
    PageSize: 50,
  });

  const [formValues, setFormValues] = useState<FormValues>({
    rejectReason: '',
    delayReason: '',
  });

  const onPressSendButton = async () => {
    try {
      if (dsHoSoLocal.length > 0) {
        await Promise.all(
          dsHoSoLocal.map(async i => {
            await handleFunction.UploadFile({
              file: i as DocumentPickerResponse,
              DonViXuLyID: user?.donViID,
              HoSoID: data?.data?.data?.thongTinThuLyHoSo?.hoSoID,
              NguoiXuLyHoSoHienTaiID:
                data?.data?.data?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID,
              NguoiXuLyID: user?.user_MasterID,
              QuaTrinhXuLyID: data?.data?.data?.quaTrinhXulyHoSo?.find(
                (_: {quaTrinhHienTai: boolean}) => _.quaTrinhHienTai === true,
              )?.quaTrinhXuLyID,
            });
          }),
        );

        // clearLocalFile();
      }
      if (
        actionType === ETypeActionDocument.Reject &&
        formValues.rejectReason
      ) {
        const rejectPayload = prepareRejectPayload(
          data?.data?.data,
          user,
          formValues.delayReason,
        );
        navigation.navigate('TransDocument', {
          item: {...params},
          rejectPayload,
        });
        return;
      } else {
        navigation.navigate('TransDocument', {
          item: {...params},
          approvePayload: {
            nguyenDoTre: formValues.delayReason,
          },
        });
        return;
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Có lỗi xảy ra vui lòng thử lại',
      });
    }
  };

  const onCancelButton = async () => {
    navigation.goBack();
  };
  const handleTextChange = (field: keyof FormValues) => (text: string) => {
    setFormValues(prev => ({...prev, [field]: text}));
  };

  const DeleteHoSoDinhKem = async (p: {
    fileName: string;
    tapTinDinhKemID: number;
  }) => {
    const {fileName, tapTinDinhKemID} = p;
    await handleFunction.DeleteFile({
      fileName: fileName,
      tapTinDinhKemID: tapTinDinhKemID,
    });
  };

  return (
    <View className="flex-1">
      <CCAppBar
        label={
          actionType === ETypeActionDocument.Reject
            ? 'Nội dung từ chối'
            : 'Nội dung phê duyệt'
        }
        isBack
      />
      <KeyboardAvoidingView
        className="flex-1"
        behavior={IsPlatformIOS ? 'padding' : undefined}>
        <ScrollView
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps={'handled'}
          contentContainerStyle={{
            paddingVertical: 8,
            paddingBottom: 80,
          }}>
          <View
            className="px-4 py-6 "
            style={{backgroundColor: theme.colors.background}}>
            <Text
              style={{
                fontSize: 17,
                fontWeight: 600,
                color: theme.colors.onBackground,
              }}>
              {(showESign && 'Văn bản cần ký') || 'Thông tin chuyển xử lý'}
            </Text>
            <Text
              style={{
                fontSize: 14,
                // color: theme.colors.outline,
              }}>
              Tài liệu giấy tờ được trình lên bởi cấp dưới
            </Text>

            {isLoadingNoiDungGiayTo || isRefetchingNoiDungGiayTo ? (
              <ActivityIndicator />
            ) : (
              dataNoiDungGiayTo?.data.data
                ?.filter((e: any) => {
                  const rs =
                    parseInt(e.createdUserID, 10) !== user?.user_MasterID;

                  return rs;
                })
                .map(
                  (
                    item: boxDocumentType | undefined,
                    i: React.Key | null | undefined,
                  ) => (
                    <View className="mt-3" key={i}>
                      <BoxDocument
                        chiTietHoSo={data?.data?.data}
                        noiDungGiayTo={item}
                        styleLabel={textStyles.text_14px_regular}
                        signStatus={true}
                        showDelete={false}
                        deleteFunction={async () => {
                          if (item?.tapTinDinhKemID && item?.uploadName) {
                            const x = {
                              fileName: item.uploadName,
                              tapTinDinhKemID: item.tapTinDinhKemID,
                            };
                            await DeleteHoSoDinhKem(x);
                            await queryClient.invalidateQueries([
                              'useDanhSachNoiDungGiayTo',
                            ]);
                          }
                        }}
                      />
                    </View>
                  ),
                )
            )}
            <View className="mt-4">
              {!!data?.data?.data?.thongTinThuLyHoSo?.noiDungXuLyTongHop
                ?.length && (
                <View className="mt-6">
                  <ExpandableText
                    label="Nội dung trình phê duyệt"
                    content={
                      data?.data?.data?.thongTinThuLyHoSo?.noiDungXuLyTongHop ||
                      ''
                    }
                  />
                </View>
              )}
            </View>
          </View>
          <LineSvg width={'100%'} height={1} />
          <View
            className="px-4 py-6"
            style={{backgroundColor: theme.colors.background}}>
            <View className="flex-row items-center justify-between">
              <View>
                <Text
                  style={{
                    fontSize: 17,
                    fontWeight: 600,
                    color: theme.colors.onBackground,
                  }}>
                  Đính kèm văn bản
                </Text>
                <Text
                  style={{
                    fontSize: 14,
                    // color: theme.colors.outline,
                  }}>
                  Danh sách giấy tờ tải lên của tôi
                </Text>
              </View>
              {data?.data?.data?.thongTinThuLyHoSo && (
                <AttachedFileButton
                  HoSoID={data?.data?.data?.thongTinThuLyHoSo?.hoSoID}
                  NguoiXuLyHoSoHienTaiID={
                    data?.data?.data?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID
                  }
                  QuaTrinhXuLyID={
                    data?.data?.data?.quaTrinhXulyHoSo?.find(
                      (_: {quaTrinhHienTai: boolean}) =>
                        _.quaTrinhHienTai === true,
                    )?.quaTrinhXuLyID
                  }
                />
              )}
            </View>
            <></>
            {isLoadingNoiDungGiayTo || isRefetchingNoiDungGiayTo ? (
              <ActivityIndicator />
            ) : (
              dataNoiDungGiayTo?.data.data
                ?.filter((e: any) => {
                  const rs =
                    parseInt(e.createdUserID, 10) === user?.user_MasterID;

                  return rs;
                })
                .map(
                  (
                    item: boxDocumentType | undefined,
                    i: React.Key | null | undefined,
                  ) => (
                    <View className="mt-3" key={i}>
                      <BoxDocument
                        chiTietHoSo={data?.data?.data}
                        noiDungGiayTo={item}
                        styleLabel={textStyles.text_14px_regular}
                        signStatus={true}
                        showDelete={true}
                        deleteFunction={async () => {
                          if (item?.tapTinDinhKemID && item?.uploadName) {
                            const x = {
                              fileName: item.uploadName,
                              tapTinDinhKemID: item.tapTinDinhKemID,
                            };
                            await DeleteHoSoDinhKem(x);
                            await queryClient.invalidateQueries([
                              'useDanhSachNoiDungGiayTo',
                            ]);
                          }
                        }}
                      />
                    </View>
                  ),
                )
            )}
          </View>
          <LineSvg width={'100%'} height={1} />

          <View
            className="px-4 py-6 "
            style={{backgroundColor: theme.colors.background}}>
            <AcTextInput
              label={'Lý do trễ'}
              mode="outlined"
              onFocus={() => {
                setTimeout(() => {
                  scrollViewRef.current?.scrollToEnd({animated: true});
                }, 0);
              }}
              value={formValues.delayReason}
              onChangeText={handleTextChange('delayReason')}
            />
          </View>

          {actionType === ETypeActionDocument.Reject && (
            <View>
              <LineSvg width={'100%'} height={1} />
              <View
                className="px-4 py-6 "
                style={{backgroundColor: theme.colors.background}}>
                <AcTextInput
                  label="Lý do từ chối (Bắt buộc)"
                  multiline
                  value={formValues.rejectReason}
                  onChangeText={handleTextChange('rejectReason')}
                  onFocus={() => {
                    setTimeout(() => {
                      scrollViewRef.current?.scrollToEnd({animated: true});
                    }, 0);
                  }}
                />
              </View>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
      <View style={styles.bottomBar}>
        <Button
          icon={() => <Icon size={24} source={Assets.ic_button_close} />}
          mode="outlined"
          className="flex flex-1"
          onPress={onCancelButton}>
          {'Huỷ bỏ'}
        </Button>
        {data?.data?.data?.thongTinThuLyHoSo && (
          <Button
            disabled={
              formValues.rejectReason?.length === 0 &&
              ETypeActionDocument.Reject === actionType
            }
            mode="contained"
            icon={() => <Icon size={24} source={Assets.ic_button_send} />}
            className="flex flex-1"
            onPress={onPressSendButton}>
            {'Gửi đi'}
          </Button>
        )}
      </View>
    </View>
  );
};

const themedStyles = (theme: MD3Theme) =>
  StyleSheet.create({
    bottomBar: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      padding: 8,
      backgroundColor: theme.colors.background,
      gap: 8,
    },
  });
