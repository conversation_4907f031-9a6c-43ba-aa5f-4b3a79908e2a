import React from 'react';
import {
  ImageBackground,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Assets} from '../../assets';
import {
  CardDocument,
  CardResultDocument,
  HeaderHome,
  TabBarHome,
  TabBarHomeData,
} from '../../components/HomeComponent';
import {
  ArchiveBookActiveIcon,
  ArchiveBookInActiveIcon,
  BuildingsActiveIcon,
  BuildingsInActiveIcon,
} from '../../icons';

import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import {useAnimatedHeaderStyles} from '../../hooks';
import {useTheme} from 'react-native-paper';
import BottomSheet, {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {AcUser, useAuthStore} from '@ac-mobile/common';
import {useUser} from '../../stores';
import {textStyles} from '../../styles/QNH_textStyle';
import {useCongChucAPIGetNguoiNhanLienThongVanBan} from '../../useQueryState/UseCongChucAPIGetNguoiNhanLienThongVanBan';

const HEIGHT_BG = 275;

const DashboardHomeScreen = () => {
  const {isLoading, handleGetListUserMaster, userMasters} = useUser();
  const {user, setUser} = useAuthStore();

  const [tab, setTab] = React.useState(0);
  const insets = useSafeAreaInsets();
  const scrollY = useSharedValue(0);
  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });
  const {headerStyle, imageStyle} = useAnimatedHeaderStyles(scrollY);
  const theme = useTheme();
  React.useEffect(() => {
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor('transparent');
      StatusBar.setTranslucent(true);
    }
    StatusBar.setBarStyle('dark-content');
    if (!isLoading) {
      handleGetListUserMaster();
    }
  }, []);

  const {
    data: dataGetNguoiNhanLienThongVanBan,
    isLoading: isLoadingGetNguoiNhanLienThongVanBan,
  } = useCongChucAPIGetNguoiNhanLienThongVanBan({
    phongBanID: user?.phongBanID || 0,
  });

  console.log(
    'dataGetNguoiNhanLienThoncgVanBan',
    user?.phongBanID,
    dataGetNguoiNhanLienThongVanBan?.data,
  );

  const tabBarData: TabBarHomeData[] = [
    {
      label: 'Của tôi',
      activeIcon: (
        <ArchiveBookActiveIcon
          color={theme.colors.primary}
          width={20}
          height={20}
        />
      ),
      inActiveIcon: <ArchiveBookInActiveIcon width={20} height={20} />,
      styleItemActive: `border-b-2 border-b-[${theme.colors.primary.toString()}] rounded-bl-lg`,
    },
    {
      label: 'Đơn vị của tôi',
      activeIcon: (
        <BuildingsActiveIcon
          color={theme.colors.primary}
          width={20}
          height={20}
        />
      ),
      inActiveIcon: <BuildingsInActiveIcon width={20} height={20} />,
      styleItemActive: `border-b-2 border-b-[${theme.colors.primary.toString()}] rounded-br-lg`,
    },
  ];

  const bottomSheetRef = React.useRef<BottomSheet>(null);

  const [selectedItem, setSelectedItem] = React.useState<any>(user); // Lưu đối tượng
  const [isSheetVisible, setIsSheetVisible] = React.useState(false);

  const handleTextPress = React.useCallback(() => {
    bottomSheetRef.current?.expand();
    setIsSheetVisible(true);
  }, []);

  const handleSelectItem = (item: AcUser) => {
    const userAuth = {
      ...user,
      ...item,
    };
    setSelectedItem(item);
    setUser(userAuth);
    handleClosePress();
  };

  const handleSheetChange = React.useCallback((index: any) => {
    if (index === -1) {
      setIsSheetVisible(false);
    } else {
      setIsSheetVisible(true);
    }
  }, []);

  const handleClosePress = React.useCallback(() => {
    bottomSheetRef.current?.close();
  }, []);

  return (
    <View
      className="flex-1"
      style={{
        backgroundColor: theme.colors.background,
      }}>
      <Animated.View
        style={[
          StyleSheet.absoluteFill,
          {
            zIndex: -999,
          },
          imageStyle,
        ]}>
        <ImageBackground
          source={Assets.bgHeader}
          imageStyle={{
            resizeMode: 'cover',
            height: HEIGHT_BG,
          }}
        />
      </Animated.View>
      <Animated.View style={[{zIndex: 999}, headerStyle]}>
        <HeaderHome
          onPress={() => handleTextPress()}
          selectedItem={selectedItem}
        />
      </Animated.View>
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: 24,
          zIndex: 999,
        }}>
        <TabBarHome
          data={tabBarData}
          indexSelected={tab}
          containerStyle={{
            marginTop: HEIGHT_BG - 68 * 2 - insets.top - 24 * 2,
            marginBottom: 16,
            backgroundColor: theme.colors.surface,
          }}
          onPress={index => {
            setTab(index);
          }}
        />
        {tab === 0 ? (
          <>
            <CardDocument />
            <CardResultDocument isMe />
          </>
        ) : (
          <CardResultDocument />
        )}
      </Animated.ScrollView>

      {isSheetVisible && (
        <TouchableWithoutFeedback onPress={handleClosePress}>
          <View style={styles.overlay} />
        </TouchableWithoutFeedback>
      )}

      <BottomSheet
        enableDynamicSizing={false}
        enableContentPanningGesture={true}
        enablePanDownToClose
        onChange={handleSheetChange}
        snapPoints={['50%']}
        ref={bottomSheetRef}
        index={-1}>
        <Text style={{textAlign: 'center'}} className={`${textStyles.h6}`}>
          Chọn đơn vị
        </Text>
        <BottomSheetScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={[styles.contentContainer]}>
          {userMasters.map((item: AcUser, index: number) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.item,
                selectedItem.user_MasterID === item.user_MasterID &&
                  styles.selectedItem,
              ]}
              onPress={() => handleSelectItem(item)}>
              <Text style={styles.itemText}>{item.tenDonVi ?? 'Chưa rõ'}</Text>
              <Text style={styles.itemText}>
                {JSON.stringify(item, null, 2)}
              </Text>
            </TouchableOpacity>
          ))}
        </BottomSheetScrollView>
      </BottomSheet>
    </View>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    // flex: 1,
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    zIndex: 1000,
    paddingBottom: 16,
  },
  item: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    borderRadius: 8,
  },
  itemText: {
    fontSize: 16,
    color: '#333',
    paddingHorizontal: 8,
  },
  selectedItem: {
    backgroundColor: 'rgba(128, 128, 128, 0.2)', // Màu xám mờ
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Gray background with transparency
    // zIndex: 998,
  },
});

export default DashboardHomeScreen;
