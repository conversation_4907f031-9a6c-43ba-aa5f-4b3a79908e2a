import {AnyObj, useAuthStore} from '@ac-mobile/common';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {RowComponent} from '../../../components/RowComponent/RowComponent';
import {ArrowSend} from '../../../icons';
import {IDanhSachHoSo} from '../../../stores';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {
  handleFunction,
  IInputDonViNhanHoSo,
  INguoiNhanChuyenHoSo,
} from '../../../api/dashboard-api';
import Toast from 'react-native-toast-message';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {
  <PERSON><PERSON>,
  Divider,
  IconButton,
  List,
  MD3Theme,
  useTheme,
} from 'react-native-paper';
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetSectionList,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import {Chip} from 'react-native-paper';
import {BottomSheetDefaultBackdropProps} from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import {useDonViNguoiNhanHoSo} from '../../../useQueryState/UseNguoiNhanChuyenHoSo';
import {useBottomSheetBackHandler} from '../Search/useBottomSheetBackHandler';
import {useMutation, useQueryClient} from 'react-query';
import {AcTextInput} from '../../../components/AcTextInput/AcTextInput';
import {commonStyle} from '../../../styles/commonStyle';

const prepareChuyenHoSoBody = ({
  item,
  loaiXuLy,
  user,
  listNguoiNhan,
}: {
  item: IDanhSachHoSo;
  loaiXuLy: number;
  user: any;
  listNguoiNhan: any[];
}) => {
  const chuyenHoSoBody = {
    hoSo: {
      hoSoID: item?.hoSoID,
      quaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
      nguoiXuLyID: item?.nguoiXuLyID ? Number.parseInt(item?.nguoiXuLyID) : 0,
      phongBanXuLyID: item?.phongBanXuLyID
        ? Number.parseInt(item?.phongBanXuLyID)
        : 0,
      donViXuLyID: user.user?.donViID,
      phongBanChuyenID: item?.phongBanXuLyID
        ? Number.parseInt(item?.phongBanXuLyID)
        : 0,
      donViChuyenID: user.user?.donViID,
      donViNhanID: 0,
      loaiXuLy,
      strDuongDiTinhTrang: item?.duongDiHoSo,
      listNguoiNhan: listNguoiNhan,
    },
  };
  return chuyenHoSoBody;
};

const TransDocument = () => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['45%', '50%', '95%'], []);
  const queryClient = useQueryClient();
  // const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [text, setText] = useState('');
  const nav = useNavigation<any>();
  const user = useAuthStore();
  const theme = useTheme();
  const {item, approvePayload, rejectPayload} =
    useRoute<RouteProp<MainStackParamList, 'TransDocument'>>().params;

  const [pick, setPick] = useState<any[]>([]);

  const {handleSheetPositionChange: handleSortSheetPositionChange} =
    useBottomSheetBackHandler(bottomSheetRef);

  const [dsNguoiNhan, setDsNguoiNhan] = useState<any[]>([]);

  // COMMENT: New code
  const donViBtsRef = useRef<BottomSheetModal>(null);
  const [textSearchDonVi, setTextSearchDonVi] = useState<string>('');
  const [dsDonVi, setDsDonVi] = useState([]);
  const [donViSelected, setDonViSelected] = useState<AnyObj | undefined>(
    undefined,
  );

  const [searchField, setSearchField] = useState('');
  const [filteredDonVi, setFilteredDonVi] = useState<any[]>([]);

  const {data: dsDonViNguoiNhan} = useDonViNguoiNhanHoSo({
    LinhVucID: item?.linhVucID || 0,
    ThuTucHanhChinhID: item?.thuTucHanhChinhID || 0,
    LoaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
    ChucNangHienTai: item?.chucNangHienTai || '',
    MaQuyTrinh: item?.maQuyTrinh || '',
    HoSoID: item?.hoSoID,
  } as IInputDonViNhanHoSo);

  useEffect(() => {
    if (dsDonViNguoiNhan && dsDonViNguoiNhan?.data?.data.length > 0) {
      setDsDonVi(
        dsDonViNguoiNhan?.data?.data.map(
          (item: {ten: any; ma: any; selected: boolean}) => {
            const value = {label: item.ten, value: item.ma};
            if (item.selected) {
              setDonViSelected(value);
            }
            return value;
          },
        ),
      );
    } else {
      setDsDonVi([]);
    }
  }, [dsDonViNguoiNhan]);

  const handleGetDsNguoiNhan = useCallback(async () => {
    try {
      if (!item || !donViSelected || !user) {
        return;
      }
      const params = {
        LinhVucID: item?.linhVucID || 0,
        ThuTucHanhChinhID: item?.thuTucHanhChinhID || 0,
        LoaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
        ChucNangHienTai: item?.chucNangHienTai || '',
        MaQuyTrinh: item?.maQuyTrinh || '',
        HoSoID: item?.hoSoID || 0,
        DonViNhanID: donViSelected?.value || 0,
        DonViID: user.user?.donViID || 0,
      } as INguoiNhanChuyenHoSo;
      const dsNguoiNhanResponse = await handleFunction.GetNguoiNhanChuyenHoSo(
        params,
      );
      if (!dsNguoiNhanResponse) {
        return;
      }
      const dsNguoiNhan = dsNguoiNhanResponse?.data?.data.map(
        (item: AnyObj) => ({
          label: item.ten,
          value: item.ma,
        }),
      );
      setDsNguoiNhan(dsNguoiNhan);
    } catch (error) {
      console.log(error);
    }
  }, [item, donViSelected, user, approvePayload, rejectPayload]);

  useEffect(() => {
    if (!donViSelected) {
      return;
    }
    handleGetDsNguoiNhan();
  }, [donViSelected, handleGetDsNguoiNhan]);

  useEffect(() => {
    if (!dsDonVi) {
      return;
    }
    const valueSearch = textSearchDonVi?.toLowerCase()?.trim();
    if (!valueSearch) {
      setFilteredDonVi(dsDonVi);
    } else {
      const regex = new RegExp(valueSearch, 'i'); // i: không phân biệt hoa thường
      const donViFilter = dsDonVi.filter((donvi: AnyObj) => {
        const label = donvi.label?.toLowerCase() || '';
        const name = donvi.name?.toLowerCase?.() || '';

        return (
          label.includes(valueSearch) || regex.test(label) || regex.test(name)
        );
      });
      setFilteredDonVi(donViFilter);
    }
  }, [textSearchDonVi, dsDonVi]);

  const renderBackdrop = useCallback(
    (
      props: React.JSX.IntrinsicAttributes & BottomSheetDefaultBackdropProps,
    ) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    [],
  );
  const {mutate: chuyenHoSoMutation, isLoading: isChuyenHoSoLoading} =
    useMutation((payload: any) => handleFunction.chuyenHoSo(payload), {
      onSuccess: responseData => {
        if (responseData?.data?.statusCode === '00') {
          Toast.show({
            type: 'success',
            text1: 'Chuyển hồ sơ thành công',
          });
          queryClient.invalidateQueries(['daNhan']);
          queryClient.invalidateQueries(['chuaNhan']);
          nav.navigate('Success', {
            name: item?.hoSoID,
            responseData: responseData.data, // Pass response data to next screen
          });
        } else {
          Toast.show({
            type: 'error',
            text1: 'Chuyển hồ sơ thất bại',
          });
        }
      },
      onError: _error => {
        Toast.show({
          type: 'error',
          text1: 'Chuyển hồ sơ thất bại',
        });
      },
    });

  const {mutate: pheDuyetHoSoMutation, isLoading: isPheDuyetLoading} =
    useMutation((payload: any) => handleFunction.pheDuyetHoSo(payload), {
      onSuccess: () => {
        Toast.show({
          type: 'success',
          text1: 'Phê duyệt thành công',
        });
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: 'Phê duyệt thất bại',
        });
      },
    });

  const {mutate: tuChoiHoSoMutation, isLoading: isTuChoiLoading} = useMutation(
    (payload: any) => handleFunction.TuChoiHoSo(payload),
    {
      onSuccess: _ => {
        Toast.show({
          type: 'success',
          text1: 'Từ chối hồ sơ thành công',
        });
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: 'Từ chối hồ sơ thất bại',
        });
      },
    },
  );

  /**
   * COMMENT: Render item đơn vị
   */
  const renderItemDonVi = useCallback(
    ({item}: {item: any}) => {
      const isSelected = donViSelected?.value === item.value;

      return (
        <List.Item
          title={item.label}
          titleNumberOfLines={0}
          titleStyle={[
            {flexWrap: 'wrap'},
            isSelected && {color: theme.colors.primary, fontWeight: 'bold'},
          ]}
          style={[
            {borderRadius: 8},
            isSelected && {backgroundColor: theme.colors.primaryContainer},
          ]}
          onPress={() => {
            if (isSelected) {
              donViBtsRef?.current?.close();
              return;
            } else {
              setDonViSelected(item);
              if (pick.length > 0) {
                setPick([]);
              }
            }
            donViBtsRef?.current?.close();
          }}
          right={props =>
            isSelected ? <List.Icon {...props} icon="check" /> : null
          }
        />
      );
    },
    [donViSelected, theme.colors, pick],
  );

  /**
   * COMMENT: Render item người nhận
   */
  const renderItem = useCallback(
    ({item}: {item: any}) => {
      const isSelected = pick.some(selected => selected.value === item.value);

      return (
        <List.Item
          title={item.label}
          titleNumberOfLines={0}
          titleStyle={{flexWrap: 'wrap'}}
          onPress={() => {
            if (isSelected) {
              setPick(pick.filter(i => i.value !== item.value));
            } else {
              setPick([...pick, item]);
            }
          }}
          right={props =>
            isSelected ? <List.Icon {...props} icon="check" /> : null
          }
        />
      );
    },
    [pick],
  );
  const handleSendOnPress = async () => {
    const newValue =
      pick.length > 0
        ? pick.map(_ => {
            return _.value;
          })
        : item?.listNguoiNhan?.length
        ? item?.listNguoiNhan.map(_ => {
            return _.value;
          })
        : dsNguoiNhan.map((_: any) => {
            return _?.value;
          });
    try {
      if (rejectPayload) {
        tuChoiHoSoMutation(
          {
            thongTinKhongPheDuyet: {
              ...rejectPayload.thongTinKhongPheDuyet,
              noiDungXuLy: text,
              lyDoKhac: text,
            },
            nguoiXuLyHoSo: {
              ...rejectPayload.nguoiXuLyHoSo,
              noiDungXuLy: text,
            },
          },
          {
            onSuccess: () => {
              const chuyenHoSoBody = prepareChuyenHoSoBody({
                item,
                loaiXuLy: 4, // Reject loaiXuLy
                user: user.user,
                listNguoiNhan: newValue,
              });
              chuyenHoSoMutation(chuyenHoSoBody);
            },
          },
        );
      }
      if (approvePayload) {
        await pheDuyetHoSoMutation(
          {
            quaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
            nguoiXuLyHoSo: {
              nguoiXuLyHoSoHienTaiID: item?.nguoiXuLyHoSoHienTaiID,
              noiDungXuLy: text,
              nguoiXuLyID: user.user?.user_PortalID,
              phongBanXuLyID: user.user?.phongBanID,
              donViXuLyID: user.user?.donViID,
              yKienCuaLanhDao: true,
              lyDoTreHanID: null,
              nguyenDoTre: approvePayload.nguyenDoTre,
            },
          },
          {
            onSuccess: () => {
              const chuyenHoSoBody = prepareChuyenHoSoBody({
                item,
                loaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
                user: user.user,
                listNguoiNhan: newValue,
              });
              chuyenHoSoMutation(chuyenHoSoBody);
            },
          },
        );
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Chuyển hồ sơ thất bại',
      });
    }
  };

  const styles = React.useMemo(() => themedStyles(theme), [theme]);
  return (
    <View style={{flex: 1}}>
      <CCAppBar label="Chuyển hồ sơ tới ..." isBack />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}>
        <View className="flex-1 mt-3.5 space-y-5 p-6 rounded-lg">
          <RowComponent>
            <AcTextInput
              label={'Nội dung xử lý (Bắt buộc)'}
              mode="outlined"
              value={text}
              onChangeText={e => {
                setText(e);
              }}
              multiline
              dismissKeyboardOnTouch
            />
          </RowComponent>
          <Divider />

          {/* Danh sách đơn vị nhận */}
          {filteredDonVi?.length > 0 && (
            <RowComponent>
              <View>
                <Button
                  mode="text"
                  textColor={theme.colors.onBackground}
                  contentStyle={styles.labelText}
                  onPress={() => bottomSheetRef.current?.present()}>
                  Chọn đơn vị nhận
                </Button>
              </View>
              {donViSelected && (
                <View style={styles.chipCustomContainer}>
                  <View style={styles.chipCustomLabelWrapper}>
                    <Text style={styles.chipCustomLabel}>
                      {donViSelected?.label || 'Chọn đơn vị nhận'}
                    </Text>
                  </View>

                  <IconButton
                    icon="close"
                    size={16}
                    onPress={() => {
                      setDonViSelected(undefined);
                      if (pick.length > 0) {
                        setPick([]);
                      }
                    }}
                    style={styles.chipCustomIcon}
                  />
                </View>
              )}
              <View>
                <Button
                  style={styles.button}
                  mode="contained"
                  onPress={() => {
                    donViBtsRef?.current?.present();
                  }}>
                  Chọn đơn vị
                </Button>
              </View>
            </RowComponent>
          )}
          {/* Danh sách người nhận */}
          {donViSelected && (
            <>
              <Divider />
              <RowComponent>
                <View>
                  <Button
                    mode="text"
                    textColor={theme.colors.onBackground}
                    contentStyle={styles.labelText}
                    onPress={() => bottomSheetRef.current?.present()}>
                    Chọn người nhận
                  </Button>
                </View>
                <View>
                  {pick.map(item => (
                    <View style={styles.chipCustomContainer}>
                      <View style={styles.chipCustomLabelWrapper}>
                        <Text style={styles.chipCustomLabel}>{item.label}</Text>
                      </View>

                      <IconButton
                        icon="close"
                        size={16}
                        onPress={() => {
                          setPick(pick.filter(i => i.value !== item.value));
                        }}
                        style={styles.chipCustomIcon}
                      />
                    </View>
                  ))}
                </View>
                <View>
                  <Button
                    style={styles.button}
                    mode="contained"
                    onPress={() => bottomSheetRef.current?.present()}>
                    Thêm người nhận
                  </Button>
                </View>
              </RowComponent>
            </>
          )}

        </View>
      </ScrollView>
      <View style={commonStyle.footer}>
        <Button
          style={commonStyle.footerButton}
          disabled={!text.length}
          onPress={handleSendOnPress}
          mode="contained"
          loading={isPheDuyetLoading || isChuyenHoSoLoading || isTuChoiLoading}
          icon={() => <ArrowSend width={24} height={24} />}>
          Gửi đi
        </Button>
      </View>

      {/* Bottom sheet modal đơn vị người nhận*/}
      <BottomSheetModalProvider>
        <BottomSheetModal
          ref={donViBtsRef}
          snapPoints={['50%']}
          index={0}
          enablePanDownToClose
          enableDismissOnClose
          onChange={handleSortSheetPositionChange}
          backdropComponent={renderBackdrop}
          android_keyboardInputMode="adjustResize">
          <BottomSheetSectionList
            ItemSeparatorComponent={Divider}
            renderSectionHeader={() => {
              return (
                <View style={{flex: 1}}>
                  <Text style={styles.bottomSheetTitle}>Chọn đon vị nhận</Text>
                  <View className="px-4 mb-2">
                    <BottomSheetTextInput
                      placeholder="Tìm kiếm "
                      placeholderTextColor={theme.colors.outline}
                      value={searchField}
                      onChangeText={setTextSearchDonVi}
                      style={styles.bottomSheetInput}
                      onFocus={() => bottomSheetRef.current?.snapToIndex(2)}
                    />
                  </View>
                </View>
              );
            }}
            renderItem={renderItemDonVi}
            contentContainerStyle={{paddingHorizontal: 8}}
            keyExtractor={item => item.value?.toString()}
            sections={[
              {
                title: '1',
                data: dsDonVi?.filter((i: any) =>
                  i.label.toLowerCase().includes(searchField.toLowerCase()),
                ),
              },
            ]}
            bounces={false}
          />
        </BottomSheetModal>
      </BottomSheetModalProvider>

      {/* Bottom sheet modal người nhận */}
      <BottomSheetModalProvider>
        <BottomSheetModal
          ref={bottomSheetRef}
          index={0}
          snapPoints={snapPoints}
          enablePanDownToClose
          enableDismissOnClose
          onChange={handleSortSheetPositionChange}
          backdropComponent={renderBackdrop}
          android_keyboardInputMode="adjustResize">
          <BottomSheetSectionList
            ItemSeparatorComponent={Divider}
            renderSectionHeader={() => {
              return (
                <View style={{flex: 1}}>
                  <Text style={styles.bottomSheetTitle}>Chọn người nhận</Text>
                  <View className="px-4 mb-2">
                    <BottomSheetTextInput
                      placeholder="Tìm kiếm "
                      placeholderTextColor={theme.colors.outline}
                      value={searchField}
                      onChangeText={setSearchField}
                      style={styles.bottomSheetInput}
                      onFocus={() => bottomSheetRef.current?.snapToIndex(2)}
                    />
                  </View>
                </View>
              );
            }}
            renderItem={renderItem}
            contentContainerStyle={{paddingHorizontal: 8}}
            keyExtractor={item => item.value?.toString()}
            sections={[
              {
                title: '1',
                data: dsNguoiNhan?.filter((i: any) =>
                  i.label.toLowerCase().includes(searchField.toLowerCase()),
                ),
              },
            ]}
            bounces={false}
          />
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </View>
  );
};
const themedStyles = (theme: MD3Theme) =>
  StyleSheet.create({
    bottomBar: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      padding: 8,
      backgroundColor: theme.colors.background,
      gap: 8,
      justifyContent: 'flex-end',
      display: 'flex',
    },
    labelText: {
      justifyContent: 'flex-start',
    },
    alignEnd: {
      flex: 1,
      alignItems: 'flex-end',
    },
    primaryChip: {
      backgroundColor: theme.colors.primaryContainer,
    },
    primaryChipText: {
      color: theme.colors.primary,
    },
    bottomSheetTitle: {
      fontSize: 20,
      fontWeight: '600',
      textAlign: 'center',
      color: theme.colors.onBackground,
      marginBottom: 16,
    },
    scrollView: {
      flex: 1,
    },
    scrollViewContent: {
      flexGrow: 1,
      paddingBottom: 70,
    },
    bottomSheetInput: {
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      borderRadius: 8,
      marginBottom: 8,
      color: theme.colors.onBackground,
    },
    button: {
      borderRadius: 8,
      flexBasis: 0,
      flexGrow: 1,
    },
    chipCustomContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surfaceVariant,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      maxWidth: '100%',
    },
    chipCustomLabelWrapper: {
      flexShrink: 1,
      flex: 1,
    },
    chipCustomLabel: {
      color: theme.colors.onBackground,
      flexShrink: 1,
      flexWrap: 'wrap',
    },
    chipCustomIcon: {
      marginLeft: 4,
    },
  });

export default TransDocument;
