import React from 'react';
import {FormProvider as RHFProvider, UseFormReturn} from 'react-hook-form';
import {View, StyleProp, ViewStyle} from 'react-native';

type Props = {
  children: React.ReactNode;
  methods: UseFormReturn<any>; // Replace `any` with your form data type
  style?: StyleProp<ViewStyle>; // Optional styling for the View
};

export const FormProvider: React.FC<Props> = ({children, methods, style}) => {
  return (
    <RHFProvider {...methods}>
      <View style={style}>{children}</View>
    </RHFProvider>
  );
};

