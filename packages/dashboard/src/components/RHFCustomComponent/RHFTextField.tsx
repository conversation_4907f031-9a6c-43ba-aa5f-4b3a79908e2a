import React, {forwardRef, useImperativeHandle, useRef} from 'react';
import {View, TextInput, StyleSheet} from 'react-native';
import {Text} from 'react-native-paper';
import {Controller, useFormContext} from 'react-hook-form';
import {useTheme} from 'react-native-paper';

type Props = {
  name: string;
  label?: string;
  placeholder?: string;
  type?: 'text' | 'number' | 'password';
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  InputProps?: {
    endAdornment?: React.ReactNode;
  };
  [key: string]: any;
};

export const RHFTextField = forwardRef<any, Props>(
  (
    {
      name,
      label,
      placeholder,
      type = 'text',
      helperText,
      required = false,
      disabled = false,
      InputProps,
      ...other
    },
    ref,
  ) => {
    const {control} = useFormContext();
    const theme = useTheme();
    const inputRef = useRef<TextInput>(null);

    useImperativeHandle(ref, () => ({
      focus: () => {
        inputRef.current?.focus();
      },
    }));

    return (
      <Controller
        name={name}
        control={control}
        render={({field: {value, onChange}, fieldState: {error}}) => (
          <View style={styles.container}>
            <View style={styles.content}>
              {label && (
                <View style={styles.label}>
                  <Text
                    variant="labelLarge"
                    style={{color: theme.colors.onSurface}}>
                    {label}
                    {required && (
                      <Text
                        variant="labelLarge"
                        style={[styles.required, {color: theme.colors.error}]}>
                        *
                      </Text>
                    )}
                  </Text>
                </View>
              )}

              <View style={styles.inputWrapper}>
                <TextInput
                  ref={inputRef}
                  style={[
                    styles.input,
                    {
                      backgroundColor: disabled
                        ? theme.colors.surfaceVariant
                        : theme.colors.surface,
                      color: theme.colors.onSurface,
                      borderColor: error
                        ? theme.colors.error
                        : theme.colors.outline || '#ccc',
                    },
                    disabled && {color: theme.colors.onSurfaceVariant},
                    InputProps?.endAdornment ? styles.endAdornmentStyle : null,
                  ]}
                  placeholder={placeholder}
                  placeholderTextColor={
                    disabled
                      ? theme.colors.onSurfaceVariant
                      : theme.colors.onSurfaceVariant
                  }
                  value={type === 'number' && value === 0 ? '' : value}
                  onChangeText={text => {
                    if (type === 'number') {
                      onChange(Number(text));
                    } else {
                      onChange(text);
                    }
                  }}
                  secureTextEntry={type === 'password'}
                  editable={!disabled}
                  {...other}
                />
                {InputProps?.endAdornment && (
                  <View style={styles.endAdornment}>
                    {InputProps.endAdornment}
                  </View>
                )}
              </View>
            </View>

            {/* Error Message */}
            {error && (
              <Text
                variant="bodySmall"
                style={[styles.errorText, {color: theme.colors.error}]}>
                {error.message}
              </Text>
            )}

            {/* Helper Text */}
            {!error && helperText && (
              <Text
                variant="bodySmall"
                style={[
                  styles.helperText,
                  {color: theme.colors.onSurfaceVariant},
                ]}>
                {helperText}
              </Text>
            )}
          </View>
        )}
      />
    );
  },
);

const styles = StyleSheet.create({
  container: {},
  content: {},
  label: {marginBottom: 4},
  required: {},
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 6,
    padding: 10,
  },
  endAdornment: {
    position: 'absolute',
    right: 8,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {fontSize: 12, marginTop: 2},
  helperText: {fontSize: 12, marginTop: 2},
  endAdornmentStyle: {paddingRight: 36},
});
