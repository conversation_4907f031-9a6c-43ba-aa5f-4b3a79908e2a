/**
 * Normalizes Vietnamese text by removing diacritics.
 * This is useful for case-insensitive searches in Vietnamese text.
 */
export const normalizeVietnameseText = (text: string): string => {
  const vietnameseMap = [
    {regex: /[áàảãạăắằẳẵặâấầẩẫậ]/g, replace: 'a'},
    {regex: /[éèẻẽẹêếềểễệ]/g, replace: 'e'},
    {regex: /[íìỉĩị]/g, replace: 'i'},
    {regex: /[óòỏõọôốồổỗộơớờởỡợ]/g, replace: 'o'},
    {regex: /[úùủũụưứừửữự]/g, replace: 'u'},
    {regex: /[ýỳỷỹỵ]/g, replace: 'y'},
    {regex: /[đ]/g, replace: 'd'},
    {regex: /[ÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬ]/g, replace: 'A'},
    {regex: /[ÉÈẺẼẸÊẾỀỂỄỆ]/g, replace: 'E'},
    {regex: /[ÍÌỈĨỊ]/g, replace: 'I'},
    {regex: /[ÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢ]/g, replace: 'O'},
    {regex: /[ÚÙỦŨỤƯỨỪỬỮỰ]/g, replace: 'U'},
    {regex: /[ÝỲỶỸỴ]/g, replace: 'Y'},
    {regex: /[Đ]/g, replace: 'D'},
  ];

  return vietnameseMap.reduce((acc, {regex, replace}) => {
    return acc.replace(regex, replace);
  }, text);
};
