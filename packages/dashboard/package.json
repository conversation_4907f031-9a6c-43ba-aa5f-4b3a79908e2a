{"name": "dashboard", "version": "0.0.1", "private": true, "scripts": {"align-deps": "rnx-align-deps --write", "android": "react-native run-android", "bundle": "pnpm bundle:ios && pnpm bundle:android", "bundle:android": "react-native webpack-bundle --platform android --entry-file index.js --dev false", "bundle:ios": "react-native webpack-bundle --platform ios --entry-file index.js --dev false", "check-deps": "rnx-align-deps", "ios": "react-native run-ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "pods": "(cd ios && bundle install && bundle exec pod install)", "pods:update": "(cd ios && bundle exec pod update)", "copy:env": "node scripts/copy-env.js", "start": "node scripts/start.js", "switch:client": "node scripts/copy-client-files.js", "start:dev": "node scripts/start.js dev", "start:uat": "node scripts/start.js uat", "start:prod": "node scripts/start.js prod", "start:standalone:dev": "node scripts/start.js dev --standalone", "start:standalone:uat": "node scripts/start.js uat --standalone", "start:standalone:prod": "node scripts/start.js prod --standalone", "test": "jest", "typecheck": "tsc"}, "dependencies": {"@ac-mobile/common": "0.3.0", "@craftzdog/react-native-buffer": "^6.0.5", "@gorhom/bottom-sheet": "^5.1.1", "@react-native-async-storage/async-storage": "1.24.0", "@react-native-community/cli-platform-android": "13.6.9", "@react-native-community/netinfo": "11.3.1", "@react-native-masked-view/masked-view": "0.3.1", "@react-navigation/bottom-tabs": "7.1.3", "@react-navigation/material-top-tabs": "7.1.0", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.1.14", "@react-navigation/stack": "7.0.18", "@signpdf/placeholder-plain": "^3.2.4", "@signpdf/signer-p12": "^3.2.4", "@signpdf/signpdf": "^3.2.4", "@signpdf/utils": "^3.2.4", "axios": "0.27.2", "base-64": "^1.0.0", "buffer": "^6.0.3", "color": "4.2.3", "jwt-decode": "3.1.2", "lottie-react-native": "6.7.2", "moment": "2.30.1", "nativewind": "2.0.11", "node-forge": "^1.3.1", "node-libs-react-native": "^1.2.1", "pdf-lib": "^1.17.1", "react": "18.2.0", "react-hook-form": "^7.59.0", "react-native": "0.74.5", "react-native-blob-util": "0.21.2", "react-native-bootsplash": "6.1.3", "react-native-calendars": "1.1291.1", "react-native-compressor": "1.10.3", "react-native-device-info": "14.0.2", "react-native-document-picker": "9.3.1", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.21.2", "react-native-image-crop-picker": "0.41.6", "react-native-modal": "13.0.1", "react-native-pager-view": "6.3.0", "react-native-paper": "5.12.5", "react-native-paper-dates": "^0.22.34", "react-native-pdf": "6.7.7", "react-native-permissions": "5.2.1", "react-native-raw-bottom-sheet": "3.0.0", "react-native-reanimated": "3.16.6", "react-native-restart": "0.0.27", "react-native-safe-area-context": "5.1.0", "react-native-screens": "4.3.0", "react-native-svg": "14.1.0", "react-native-tab-view": "4.0.5", "react-native-toast-message": "2.2.1", "react-native-vector-icons": "10.2.0", "react-native-webview": "13.12.5", "react-native-wheel-scrollview-picker": "2.0.6", "react-query": "^3.39.3", "semver": "7.6.3", "zustand": "^5.0.2"}, "devDependencies": {"@ac-mobile/sdk": "0.0.17", "@babel/core": "7.25.9", "@babel/preset-env": "7.25.9", "@babel/runtime": "7.25.9", "@callstack/repack": "^4.3.3", "@react-native-community/cli": "13.6.9", "@react-native-community/cli-platform-android": "13.6.9", "@react-native/babel-preset": "0.74.87", "@react-native/eslint-config": "0.74.87", "@react-native/gradle-plugin": "0.74.87", "@react-native/metro-config": "0.74.87", "@react-native/typescript-config": "0.74.87", "@rnx-kit/align-deps": "3.0.1", "@tsconfig/react-native": "^2.0.3", "@types/base-64": "^1.0.2", "@types/jest": "^29.2.1", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "@types/semver": "7.5.8", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "babel-jest": "^29.6.3", "babel-loader": "^9.2.1", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "tailwindcss": "3.3.2", "terser-webpack-plugin": "^5.3.10", "typescript": "5.0.4", "webpack": "^5.95.0"}, "engines": {"node": ">=18"}, "federatedDependencies": [{"name": "auth", "type": "internal", "url": "https://dl-storage.uat.lokify.xplat.online/ihan<PERSON>-staff"}], "federatedModule": "dashboard", "rnx-kit": {"kitType": "app", "alignDeps": {"presets": ["./node_modules/@ac-mobile/sdk/preset"], "requirements": ["@ac-mobile/sdk@0.0.15"], "capabilities": ["super-app"]}}}